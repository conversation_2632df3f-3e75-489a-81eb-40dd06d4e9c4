def show_all_users():
    print("")
def search_user():
    print("")
def add_user():
    print("CREATE NEW USER")
    name = ""
    age = ""
    sex = ""
    while True:
        name = input("Name: ")
        if (name.__str__() != ""):
            break
    print("Your name: ", name)

def update_user():
    print("")
def delete_user():
    print("")

def show_system_menu():
    print("|-----------------------------------------|")
    print("|            SELECT A FEATURE             |")
    print("|-----------------------------------------|")
    print("|1. Show all users                        |")
    print("|2. Search users                          |")
    print("|3. Add user                              |")
    print("|4. Update user                           |")
    print("|5. Delete user                           |")
    print("|-----------------------------------------|")

    while True:
        select = int(input("YOUR SELECTION: "))
        match select:
            case 1:
                show_all_users()
                break
            case 2:
                search_user()
                break
            case 3:
                add_user()
                break
            case 4:
                update_user()
                break
            case 5:
                delete_user()
                break
            case _:
                print("Invalid selection")


if __name__ == '__main__':
    print("Welcome to the User management system!")
    show_system_menu()



